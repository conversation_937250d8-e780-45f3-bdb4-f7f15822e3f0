/**
 * V2 Custom Field System Verification Script
 *
 * Simple verification script to test the core components of the v2 system.
 * This helps ensure everything is working correctly before moving to Phase 2.
 *
 * @fileoverview v2 System verification
 * @version 2.0.0
 * @since 2024-08-07
 */

import { FieldMatcher } from "./core/fieldMatcher.js";
import { ValueConverter } from "./core/valueConverter.js";
import { StandardFieldMapper } from "./core/standardFieldMapper.js";
import { checkTypeCompatibility } from "./config/fieldMappings.js";
import type { APGetCustomFieldType } from "@/type/APTypes.js";
import type { GetCCCustomField } from "@/type/CCTypes.js";

/**
 * Mock AP field for testing
 */
const mockAPField: APGetCustomFieldType = {
	id: "ap-email-field",
	name: "Patient Email",
	label: "Patient Email Address",
	dataType: "EMAIL",
	required: false,
	options: [],
};

/**
 * Mock CC field for testing
 */
const mockCCField: GetCCCustomField = {
	id: 123,
	name: "patient_email",
	label: "Patient Email",
	type: "email",
	allowMultipleValues: false,
	required: false,
	picklistOptions: [],
};

/**
 * Mock TEXTBOX_LIST field for testing
 */
const mockTextboxListField: APGetCustomFieldType = {
	id: "ap-textbox-field",
	name: "Multiple Phones",
	label: "Multiple Phone Numbers",
	dataType: "TEXTBOX_LIST",
	required: false,
	options: [],
};

/**
 * Mock multi-value CC field for testing
 */
const mockMultiValueCCField: GetCCCustomField = {
	id: 456,
	name: "multiple_phones",
	label: "Multiple Phones",
	type: "text",
	allowMultipleValues: true,
	required: false,
	picklistOptions: [],
};

/**
 * Test field matching functionality
 */
function testFieldMatching(): void {
	console.log("🔍 Testing Field Matching...");
	
	const matcher = new FieldMatcher();
	
	// Test exact match
	const exactMatch = matcher.matchFields(mockAPField, [mockCCField]);
	console.log("✅ Exact match result:", {
		matched: exactMatch.matched,
		matchType: exactMatch.matchType,
		confidence: exactMatch.confidence,
		typeCompatible: exactMatch.typeCompatible,
	});
	
	// Test fuzzy matching with similar names
	const fuzzyMatcher = new FieldMatcher({ strategy: "fuzzy" as any, fuzzyThreshold: 0.7 });
	const fuzzyMatch = fuzzyMatcher.matchFields(
		{ ...mockAPField, label: "Patient E-Mail" },
		[mockCCField]
	);
	console.log("✅ Fuzzy match result:", {
		matched: fuzzyMatch.matched,
		matchType: fuzzyMatch.matchType,
		confidence: fuzzyMatch.confidence,
	});
}

/**
 * Test value conversion functionality
 */
function testValueConversion(): void {
	console.log("\n🔄 Testing Value Conversion...");
	
	const converter = new ValueConverter();
	
	// Test TEXTBOX_LIST to multi-value text conversion
	const textboxListValue = {
		"0": "<EMAIL>",
		"1": "<EMAIL>",
		"2": "<EMAIL>",
	};
	
	const textboxResult = converter.convertValue({
		sourceType: "TEXTBOX_LIST",
		targetType: "text",
		sourceValue: textboxListValue,
		isMultiValue: true,
	});
	
	console.log("✅ TEXTBOX_LIST → text conversion:", {
		success: textboxResult.success,
		originalValue: textboxResult.originalValue,
		convertedValue: textboxResult.convertedValue,
		conversionType: textboxResult.conversionType,
	});
	
	// Test multi-value text to TEXTBOX_LIST conversion
	const multiTextValue = "value1 | value2 | value3";
	
	const multiTextResult = converter.convertValue({
		sourceType: "text",
		targetType: "TEXTBOX_LIST",
		sourceValue: multiTextValue,
		isMultiValue: true,
	});
	
	console.log("✅ Multi-text → TEXTBOX_LIST conversion:", {
		success: multiTextResult.success,
		originalValue: multiTextResult.originalValue,
		convertedValue: multiTextResult.convertedValue,
		conversionType: multiTextResult.conversionType,
	});
	
	// Test RADIO to boolean conversion
	const radioResult = converter.convertValue({
		sourceType: "RADIO",
		targetType: "boolean",
		sourceValue: "Yes",
	});
	
	console.log("✅ RADIO → boolean conversion:", {
		success: radioResult.success,
		originalValue: radioResult.originalValue,
		convertedValue: radioResult.convertedValue,
	});
	
	// Test empty string filtering
	const emptyStringResult = converter.convertValue({
		sourceType: "EMAIL",
		targetType: "email",
		sourceValue: "",
	});
	
	console.log("✅ Empty string filtering:", {
		success: emptyStringResult.success,
		originalValue: emptyStringResult.originalValue,
		convertedValue: emptyStringResult.convertedValue,
		warnings: emptyStringResult.warnings,
	});
}

/**
 * Test type compatibility checking
 */
function testTypeCompatibility(): void {
	console.log("\n🔧 Testing Type Compatibility...");
	
	// Test compatible types
	const emailCompatibility = checkTypeCompatibility("EMAIL", "email");
	console.log("✅ EMAIL → email compatibility:", emailCompatibility);
	
	// Test TEXTBOX_LIST compatibility
	const textboxCompatibility = checkTypeCompatibility("TEXTBOX_LIST", "text", undefined, true);
	console.log("✅ TEXTBOX_LIST → multi-text compatibility:", textboxCompatibility);
	
	// Test incompatible types
	const incompatibleTypes = checkTypeCompatibility("FILE_UPLOAD", "boolean");
	console.log("✅ FILE_UPLOAD → boolean incompatibility:", incompatibleTypes);
}

/**
 * Test standard field mapping
 */
function testStandardFieldMapping(): void {
	console.log("\n📋 Testing Standard Field Mapping...");
	
	const standardMapper = new StandardFieldMapper();
	
	// Test AP patient data extraction
	const apPatientData = {
		email: "<EMAIL>",
		phone: "+**********",
		contactInfo: {
			email: "<EMAIL>",
		},
	};
	
	const apExtractions = standardMapper.extractStandardFields(apPatientData, "ap");
	console.log("✅ AP standard field extractions:", apExtractions);
	
	// Test CC patient data extraction
	const ccPatientData = {
		id: "cc-patient-123",
		profileUrl: "https://clinicore.com/patient/123",
	};
	
	const ccExtractions = standardMapper.extractStandardFields(ccPatientData, "cc");
	console.log("✅ CC standard field extractions:", ccExtractions);
}

/**
 * Run all verification tests
 */
export function runVerification(): void {
	console.log("🚀 Starting V2 Custom Field System Verification...\n");
	
	try {
		testFieldMatching();
		testValueConversion();
		testTypeCompatibility();
		testStandardFieldMapping();
		
		console.log("\n✅ All verification tests completed successfully!");
		console.log("🎉 V2 Custom Field System core components are working correctly.");
		
	} catch (error) {
		console.error("\n❌ Verification failed:", error);
		throw error;
	}
}

// Run verification if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	runVerification();
}
