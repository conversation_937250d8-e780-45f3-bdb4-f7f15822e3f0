/**
 * Standard Field Mapping Configuration v2
 *
 * Configuration for mapping standard platform fields to custom fields.
 * Handles AP standard fields (email, phone) → CC custom fields and
 * CC standard fields (patientId, profile links) → AP custom fields.
 *
 * @fileoverview v2 Standard field mapping configuration
 * @version 2.0.0
 * @since 2024-08-07
 */

import type {
	StandardFieldMapping,
	StandardFieldType,
	APFieldDataType,
	CCFieldType,
	Platform,
} from "../types/index.js";

/**
 * Standard field extraction configuration
 */
export interface StandardFieldExtractor {
	fieldType: StandardFieldType;
	platform: Platform;
	extractionPath: string;
	fallbackPaths?: string[];
	validator?: (value: unknown) => boolean;
	transformer?: (value: unknown) => unknown;
	required?: boolean;
}

/**
 * AP standard field extractors
 * These extract standard fields from AP patient data to sync to CC custom fields
 */
export const AP_STANDARD_EXTRACTORS: StandardFieldExtractor[] = [
	{
		fieldType: "email",
		platform: "ap",
		extractionPath: "email",
		fallbackPaths: ["contactInfo.email", "primaryEmail"],
		validator: (value) => 
			typeof value === "string" && 
			value.includes("@") && 
			value.length > 0,
		transformer: (value) => typeof value === "string" ? value.trim().toLowerCase() : value,
		required: false,
	},
	{
		fieldType: "phone",
		platform: "ap",
		extractionPath: "phone",
		fallbackPaths: ["contactInfo.phone", "primaryPhone", "phoneNumber"],
		validator: (value) => 
			typeof value === "string" && 
			value.replace(/\D/g, "").length >= 7, // At least 7 digits
		transformer: (value) => typeof value === "string" ? value.trim() : value,
		required: false,
	},
];

/**
 * CC standard field extractors
 * These extract standard fields from CC patient data to sync to AP custom fields
 */
export const CC_STANDARD_EXTRACTORS: StandardFieldExtractor[] = [
	{
		fieldType: "patientId",
		platform: "cc",
		extractionPath: "id",
		fallbackPaths: ["patientId", "patient_id"],
		validator: (value) => 
			(typeof value === "string" || typeof value === "number") && 
			String(value).length > 0,
		transformer: (value) => String(value),
		required: true,
	},
	{
		fieldType: "ccProfileLink",
		platform: "cc",
		extractionPath: "profileUrl",
		fallbackPaths: ["profile_url", "url"],
		validator: (value) => 
			typeof value === "string" && 
			(value.startsWith("http") || value.startsWith("/")),
		transformer: (value) => typeof value === "string" ? value.trim() : value,
		required: false,
	},
];

/**
 * Default standard field mappings
 * These define how standard fields should be mapped to custom fields
 */
export const DEFAULT_STANDARD_MAPPINGS: StandardFieldMapping[] = [
	// AP email → CC custom field
	{
		standardField: "email",
		platform: "cc",
		customFieldName: "Email",
		customFieldType: "email",
		extractionPath: "email",
		required: false,
	},
	// AP phone → CC custom field
	{
		standardField: "phone",
		platform: "cc",
		customFieldName: "Phone",
		customFieldType: "telephone",
		extractionPath: "phone",
		required: false,
	},
	// CC patient ID → AP custom field
	{
		standardField: "patientId",
		platform: "ap",
		customFieldName: "CC Patient ID",
		customFieldType: "TEXT",
		extractionPath: "id",
		required: true,
	},
	// CC profile link → AP custom field
	{
		standardField: "ccProfileLink",
		platform: "ap",
		customFieldName: "CC Profile Link",
		customFieldType: "TEXT",
		extractionPath: "profileUrl",
		required: false,
	},
];

/**
 * Standard field naming conventions
 */
export const STANDARD_FIELD_NAMES = {
	AP_EMAIL_IN_CC: "AP Email",
	AP_PHONE_IN_CC: "AP Phone",
	CC_PATIENT_ID_IN_AP: "CC Patient ID",
	CC_PROFILE_LINK_IN_AP: "CC Profile Link",
} as const;

/**
 * Standard field type mappings
 */
export const STANDARD_FIELD_TYPE_MAP: Record<
	StandardFieldType,
	{ apType: APFieldDataType; ccType: CCFieldType }
> = {
	email: { apType: "EMAIL", ccType: "email" },
	phone: { apType: "PHONE", ccType: "telephone" },
	patientId: { apType: "TEXT", ccType: "text" },
	ccProfileLink: { apType: "TEXT", ccType: "text" },
};

/**
 * Get standard field extractor by type and platform
 */
export function getStandardFieldExtractor(
	fieldType: StandardFieldType,
	platform: Platform,
): StandardFieldExtractor | null {
	const extractors = platform === "ap" ? AP_STANDARD_EXTRACTORS : CC_STANDARD_EXTRACTORS;
	return extractors.find(
		(extractor) => 
			extractor.fieldType === fieldType && 
			extractor.platform === platform
	) || null;
}

/**
 * Get default standard field mapping
 */
export function getDefaultStandardMapping(
	standardField: StandardFieldType,
	targetPlatform: Platform,
): StandardFieldMapping | null {
	return DEFAULT_STANDARD_MAPPINGS.find(
		(mapping) => 
			mapping.standardField === standardField && 
			mapping.platform === targetPlatform
	) || null;
}

/**
 * Extract standard field value from patient data
 */
export function extractStandardFieldValue(
	patientData: Record<string, unknown>,
	extractor: StandardFieldExtractor,
): unknown {
	// Try main extraction path
	let value = getNestedValue(patientData, extractor.extractionPath);
	
	// Try fallback paths if main path fails
	if ((value === undefined || value === null || value === "") && extractor.fallbackPaths) {
		for (const fallbackPath of extractor.fallbackPaths) {
			value = getNestedValue(patientData, fallbackPath);
			if (value !== undefined && value !== null && value !== "") {
				break;
			}
		}
	}
	
	// Validate the value
	if (extractor.validator && !extractor.validator(value)) {
		return null;
	}
	
	// Transform the value
	if (extractor.transformer && value !== null && value !== undefined) {
		value = extractor.transformer(value);
	}
	
	return value;
}

/**
 * Check if a field name matches a standard field pattern
 */
export function isStandardFieldName(fieldName: string): StandardFieldType | null {
	const normalizedName = fieldName.toLowerCase().trim();
	
	// Check for AP email patterns
	if (normalizedName.includes("ap") && normalizedName.includes("email")) {
		return "email";
	}
	
	// Check for AP phone patterns
	if (normalizedName.includes("ap") && (normalizedName.includes("phone") || normalizedName.includes("telefon"))) {
		return "phone";
	}
	
	// Check for CC patient ID patterns
	if (normalizedName.includes("cc") && (normalizedName.includes("patient") || normalizedName.includes("id"))) {
		return "patientId";
	}
	
	// Check for CC profile link patterns
	if (normalizedName.includes("cc") && (normalizedName.includes("profile") || normalizedName.includes("link"))) {
		return "ccProfileLink";
	}
	
	return null;
}

/**
 * Generate standard field name for target platform
 */
export function generateStandardFieldName(
	standardField: StandardFieldType,
	targetPlatform: Platform,
): string {
	const mapping = getDefaultStandardMapping(standardField, targetPlatform);
	return mapping?.customFieldName || `${standardField}_${targetPlatform}`;
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj: Record<string, unknown>, path: string): unknown {
	return path.split('.').reduce((current, key) => {
		if (current && typeof current === 'object' && key in current) {
			return (current as Record<string, unknown>)[key];
		}
		return undefined;
	}, obj);
}

/**
 * Validate standard field value based on type
 */
export function validateStandardFieldValue(
	value: unknown,
	fieldType: StandardFieldType,
): boolean {
	const extractor = getStandardFieldExtractor(fieldType, "ap") || 
					 getStandardFieldExtractor(fieldType, "cc");
	
	if (!extractor?.validator) {
		// Basic validation - not null/undefined/empty string
		return value !== null && value !== undefined && value !== "";
	}
	
	return extractor.validator(value);
}

/**
 * Transform standard field value based on type
 */
export function transformStandardFieldValue(
	value: unknown,
	fieldType: StandardFieldType,
): unknown {
	const extractor = getStandardFieldExtractor(fieldType, "ap") || 
					 getStandardFieldExtractor(fieldType, "cc");
	
	if (!extractor?.transformer) {
		return value;
	}
	
	return extractor.transformer(value);
}
